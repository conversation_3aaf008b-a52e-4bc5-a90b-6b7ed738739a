import axios from 'axios'
import type { AxiosResponse } from 'axios'

// API 响应格式
interface ApiResponse<T = unknown> {
  code: 0 | 1
  message: string
  data: T
}

// 分页参数
interface PaginationParams {
  pageNum: number
  pageSize: number
}

// 智能描述搜索参数
interface DescriptionSearchParams extends PaginationParams {
  namespace?: string
  publishStatus?: string
  materialVersion?: string
}

// 任务搜索参数
interface JobSearchParams extends PaginationParams {
  namespace?: string
  staging?: string
  materialVersion?: string
}

// 分页结果
interface PaginationResult<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
  totalPage: number
  hasNext: boolean
  hasPrev: boolean
}

// 物料基础信息
interface MaterialDetail {
  title: string // 中文名称
  name: string // 英文名称（组件名称）
  namespace: string
}

// ZhiDa 需要的物料描述格式
interface ZhiDaNeededMaterialDescription {
  namespace: string
  title: string // 中文名称
  name: string // 英文名称（组件名称）
  description: string // 功能描述
  propsDefine: string // 属性定义
  jsxDemo: string[] // JSX 示例
  // 扩展字段
  childNested?: string[] // 子组件嵌套
  jsxPropCompatible?: Record<string, unknown> // JSX 属性兼容
  mergePropsBeforeInsert?: Record<string, unknown> // 插入前属性合并
  purePropEffect?: Array<{ // 属性二次适配
    predicate: string
    alias: {
      key: string
      value: unknown
    }
  }>
}

// 更新 ZhiDa 描述的 DTO
interface UpdateZhidaDescriptionDto {
  description?: string // 功能描述
  propsDefine?: string // 属性定义
  jsxDemo?: string[] // JSX 示例
  childNested?: string[] // 子组件嵌套
  jsxPropCompatible?: Record<string, unknown> // JSX 属性兼容
  mergePropsBeforeInsert?: Record<string, unknown> // 插入前属性合并
  purePropEffect?: Array<{ // 属性二次适配
    predicate: string
    alias: {
      key: string
      value: unknown
    }
  }>
}

// 发布草稿描述的 DTO
interface PublishDraftDescriptionDto {
  updateContent?: UpdateZhidaDescriptionDto // 可选的内容更新
}

// 物料智能描述
interface MaterialSmartDescription {
  id: number
  materialId: number
  materialVersion: string
  materialPubId: number
  jobId: number
  smartDescription: unknown
  zhidaDescription: ZhiDaNeededMaterialDescription
  createTime: number
  state: number
  publishStatus: number // 发布状态：0-草稿，1-正式发布
  materialDetail: MaterialDetail
}

// 物料智能描述任务
interface MaterialSmartDescriptionJob {
  id: number
  materialId: number
  materialVersion: string
  materialPubId: number
  resultId?: number | null
  staging: string
  rawConversation?: string | null
  rawResult?: string | null
  failedReason?: string | null
  createTime: number
  state: number
  materialDetail: MaterialDetail
}

// 按物料分组的描述数据
interface MaterialGroupedDescription {
  materialId: number
  materialDetail: MaterialDetail
  totalVersions: number
  latestVersion: string
  versions: MaterialSmartDescription[]
}

// 按物料分组的描述列表结果
interface MaterialGroupedDescriptionList {
  list: MaterialGroupedDescription[]
  total: number
  pageNum: number
  pageSize: number
  totalPage: number
  hasNext: boolean
  hasPrev: boolean
}

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api', // 假设后端 API 基础路径为 /api
  timeout: 10000,
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url, config.params)
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log('API Response:', response.status, response.data)
    return response
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data || error.message)
    return Promise.reject(error)
  },
)

// 物料智能描述相关 API
export const materialSmartDescriptionApi = {
  // 获取按物料分组的描述列表
  getDescriptions: (params: DescriptionSearchParams) =>
    api.get<ApiResponse<MaterialGroupedDescriptionList>>('/material-smart-description', { params }),

  // 根据 ID 获取单个描述详情
  getDescriptionById: (id: number) =>
    api.get<ApiResponse<MaterialSmartDescription>>(`/material-smart-description/${id}`),

  // 根据物料 ID 获取历史版本
  getHistoryByMaterialId: (materialId: number, params: PaginationParams) =>
    api.get<ApiResponse<PaginationResult<MaterialSmartDescription>>>(
      `/material-smart-description/material/${materialId}/history`,
      { params },
    ),

  // 更新 ZhiDa 描述内容
  updateZhidaDescription: (id: number, data: UpdateZhidaDescriptionDto) =>
    api.put<ApiResponse<MaterialSmartDescription>>(`/material-smart-description/${id}/zhida-description`, data),

  // 发布草稿描述
  publishDraftDescription: (id: number, data?: PublishDraftDescriptionDto) =>
    api.post<ApiResponse<MaterialSmartDescription>>(`/material-smart-description/${id}/publish`, data),
}

// 物料智能描述任务相关 API
export const materialSmartDescriptionJobApi = {
  // 获取物料智能描述任务列表
  getJobs: (params: JobSearchParams) => {
    return api.get<ApiResponse<PaginationResult<MaterialSmartDescriptionJob>>>(
      '/material-smart-description/job',
      {
        params: {
          ...params,
        },
      },
    )
  },

  // 触发新的智能描述任务
  triggerJob: (data: {
    material: {
      materialId?: number
      namespace?: string
      materialVersionName?: string
    }
    options: {
      invokeLangBridgeBizKey: string
      invokeLangBridgeOperator: string
    }
  }) =>
    api.post<ApiResponse<number>>('/material-smart-description/job/trigger', data),

  // 获取任务统计信息
  getJobStats: () =>
    api.get<ApiResponse<{
      pending: number
      succeeded: number
      failed: number
      total: number
    }>>('/material-smart-description/job/stats/overview'),

  // 根据物料 ID 获取任务列表
  getJobsByMaterialId: (materialId: number, params: PaginationParams) =>
    api.get<ApiResponse<PaginationResult<MaterialSmartDescriptionJob>>>(
      `/material-smart-description/job/material/${materialId}`,
      { params },
    ),

  // 根据 materialPubId 获取历史记录
  getHistoryByMaterialPubId: (materialPubId: number, params: PaginationParams) => {
    return api.get<ApiResponse<PaginationResult<MaterialSmartDescriptionJob>>>(
      `/material-smart-description/job/history/${materialPubId}`,
      {
        params: {
          ...params,
        },
      },
    )
  },
}

export default api

// 导出类型供其他文件使用
export type {
  ApiResponse,
  PaginationParams,
  PaginationResult,
  DescriptionSearchParams,
  JobSearchParams,
  MaterialDetail,
  ZhiDaNeededMaterialDescription,
  UpdateZhidaDescriptionDto,
  PublishDraftDescriptionDto,
  MaterialSmartDescription,
  MaterialSmartDescriptionJob,
  MaterialGroupedDescription,
  MaterialGroupedDescriptionList,
}
