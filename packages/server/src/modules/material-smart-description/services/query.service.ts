import { Injectable, Logger } from '@nestjs/common'
import { Op } from '@infra-node/sequelize'

import { DatabaseService } from '../../../database/database.service'
import { MaterialSmartDescription } from '../../../database/models'

/**
 * 物料智能描述查询服务
 * 专门负责各种查询操作，遵循单一职责原则
 */
@Injectable()
export class MaterialSmartDescriptionQueryService {
  private readonly logger = new Logger(
    MaterialSmartDescriptionQueryService.name,
  )

  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 根据物料 ID 查找描述
   */
  async findDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据物料 ID 查找描述（包含所有状态的记录，用于历史查询）
   */
  async findDescriptionsByMaterialIdWithAllStates(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          // 不过滤 state，查询所有状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找所有状态描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据物料 ID 和版本查找最新的有效描述
   */
  async findLatestDescriptionByMaterial(
    materialId: number,
    materialVersion?: string,
  ): Promise<MaterialSmartDescription | null> {
    return this.findDescription({
      materialId,
      materialVersion,
    })
  }

  /**
   * 通用查询描述方法
   * 根据提供的选项查找描述记录
   */
  async findDescription(
    options: Service.MaterialSmartDescription.FindDescriptionOptions,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: options.state ?? 1, // 默认只查询正常状态的记录
      }

      // 根据传入的参数构建查询条件
      if (options.materialId !== undefined) {
        whereCondition.materialId = options.materialId
      }

      if (options.namespace !== undefined) {
        whereCondition.namespace = options.namespace
      }

      if (options.materialVersion !== undefined) {
        whereCondition.materialVersion = options.materialVersion
      }

      if (options.publishStatus !== undefined) {
        whereCondition.publishStatus = options.publishStatus
      }

      if (options.id !== undefined) {
        whereCondition.id = options.id
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`通用查询描述失败 options: ${JSON.stringify(options)}`, error)
      throw error
    }
  }

  /**
   * 通用查询多个描述方法
   * 根据提供的选项查找描述记录列表
   */
  async findDescriptions(
    options: Service.MaterialSmartDescription.FindDescriptionsOptions,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: options.state ?? 1, // 默认只查询正常状态的记录
      }

      // 根据传入的参数构建查询条件
      if (options.materialId !== undefined) {
        whereCondition.materialId = options.materialId
      }

      if (options.namespace !== undefined) {
        whereCondition.namespace = options.namespace
      }

      if (options.materialVersion !== undefined) {
        whereCondition.materialVersion = options.materialVersion
      }

      if (options.publishStatus !== undefined) {
        whereCondition.publishStatus = options.publishStatus
      }

      if (options.id !== undefined) {
        whereCondition.id = options.id
      }

      const queryOptions = {
        where: whereCondition,
        order: [['createTime', 'DESC']] as [string, string][],
        ...(options.limit !== undefined && { limit: Number(options.limit) }),
        ...(options.offset !== undefined && { offset: Number(options.offset) }),
      }

      const descriptions = await models.MaterialSmartDescription.findAll(queryOptions)

      return descriptions.map(desc => desc.toJSON() as MaterialSmartDescription)
    }
    catch (error) {
      this.logger.error(`通用查询描述列表失败 options: ${JSON.stringify(options)}`, error)
      throw error
    }
  }

  /**
   * 根据物料标识参数查找最新的有效描述
   */
  async findLatestDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    return this.findDescription({
      materialId: params.materialId,
      namespace: params.namespace,
      materialVersion: params.materialVersionName,
    })
  }

  /**
   * 根据物料标识参数查找最新的有效且已发布的描述
   * 包含 state = 1 和 publishStatus = 1 的检查
   */
  async findLatestPublishedDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    return this.findDescription({
      materialId: params.materialId,
      namespace: params.namespace,
      materialVersion: params.materialVersionName,
      publishStatus: 1,
    })
  }

  /**
   * 根据物料 ID 查找所有有效且已发布的描述
   * 用于版本比较和回退查找
   */
  async findAllPublishedDescriptionsByMaterialId(
    materialId: number,
  ): Promise<MaterialSmartDescription[]> {
    return this.findDescriptions({
      materialId,
      publishStatus: 1,
    })
  }

  /**
   * 根据 namespace 查找描述（支持模糊查询）
   */
  async findDescriptionsByNamespace(
    namespace: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`, // 支持模糊查询
          },
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 模糊查找描述失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 ID 查找单个描述记录
   */
  async findDescriptionById(
    id: number,
  ): Promise<MaterialSmartDescription | null> {
    return this.findDescription({ id })
  }

  /**
   * 根据 namespace 和 version 精确查找描述
   */
  async findDescriptionByNamespaceAndVersion(
    namespace: string,
    version: string,
  ): Promise<MaterialSmartDescription | null> {
    return this.findDescription({
      namespace,
      materialVersion: version,
    })
  }

  /**
   * 分页查询描述列表
   */
  async findDescriptionsWithPagination(
    params: Service.PaginationParams & {
      materialId?: number
      namespace?: string
      publishStatus?: number
    },
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    try {
      const models = this.databaseService.getModels()
      const { pageNum, pageSize, materialId, namespace, publishStatus }
        = params

      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
      }

      if (materialId) {
        whereCondition.materialId = materialId
      }

      if (namespace) {
        whereCondition.namespace = {
          [Op.like]: `%${namespace}%`,
        }
      }

      if (publishStatus !== undefined) {
        whereCondition.publishStatus = publishStatus
      }

      const offset = (pageNum - 1) * pageSize
      const { count, rows }
        = await models.MaterialSmartDescription.findAndCountAll({
          where: whereCondition,
          limit: pageSize,
          offset,
          order: [['createTime', 'DESC']],
        })

      const list = rows.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
      const totalPage = Math.ceil(count / pageSize)

      return {
        list,
        total: count,
        pageNum,
        pageSize,
        totalPage,
        hasNext: pageNum < totalPage,
        hasPrev: pageNum > 1,
      }
    }
    catch (error) {
      this.logger.error('分页查询描述列表失败', error)
      throw error
    }
  }

  /**
   * 根据命名空间统计描述数量
   */
  async countDescriptionsByNamespace(namespace: string): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescription.count({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`,
          },
          state: 1,
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据命名空间统计描述数量失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据发布状态查找描述
   */
  async findDescriptionsByPublishStatus(
    publishStatus: number,
    limit: number,
    offset: number,
  ): Promise<MaterialSmartDescription[]> {
    return this.findDescriptions({
      publishStatus,
      limit,
      offset,
    })
  }

  /**
   * 根据发布状态统计描述数量
   */
  async countDescriptionsByPublishStatus(
    publishStatus: number,
  ): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescription.count({
        where: {
          publishStatus,
          state: 1,
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据发布状态统计描述数量失败 publishStatus: ${publishStatus}`,
        error,
      )
      throw error
    }
  }

  /**
   * 获取按物料分组的描述数据（原始数据）
   */
  async getGroupedDescriptionsRaw(
    params: Service.PaginationParams & {
      namespace?: string
      publishStatus?: number
      materialVersion?: string
    },
  ): Promise<{
      materialIds: number[]
      total: number
      pageNum: number
      pageSize: number
      totalPage: number
      hasNext: boolean
      hasPrev: boolean
    }> {
    const { pageNum = 1, pageSize = 10, namespace, publishStatus, materialVersion } = params
    const offset = (pageNum - 1) * pageSize

    try {
      // 构建筛选条件和参数
      const whereConditions = ['state = :state']
      const replacements: Record<string, string | number> = {
        state: 1,
      }

      if (namespace) {
        whereConditions.push('namespace LIKE :namespace')
        replacements.namespace = `%${namespace}%`
      }
      if (publishStatus !== undefined) {
        whereConditions.push('publish_status = :publishStatus')
        replacements.publishStatus = publishStatus
      }
      if (materialVersion) {
        whereConditions.push('material_version LIKE :materialVersion')
        replacements.materialVersion = `%${materialVersion}%`
      }
      const whereClause = whereConditions.join(' AND ')

      // 获取不重复的 materialId 列表（分页）
      const materialIdsSql = `
        SELECT DISTINCT material_id
        FROM material_smart_description
        WHERE ${whereClause}
        ORDER BY material_id DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `

      // 获取总的物料数量
      const countSql = `
        SELECT COUNT(DISTINCT material_id) as total
        FROM material_smart_description
        WHERE ${whereClause}
      `

      const [materialIdsResults, countResults] = await Promise.all([
        this.databaseService.query(materialIdsSql, { replacements }),
        this.databaseService.query(countSql, { replacements }),
      ])

      const materialIds = (materialIdsResults as { material_id: number }[]).map(
        row => row.material_id,
      )
      const total
        = ((countResults as unknown[])[0] as { total: number })?.total || 0
      const totalPage = Math.ceil(total / pageSize)

      return {
        materialIds,
        total,
        pageNum,
        pageSize,
        totalPage,
        hasNext: pageNum < totalPage,
        hasPrev: pageNum > 1,
      }
    }
    catch (error) {
      this.logger.error('获取分组描述列表失败', error)
      throw error
    }
  }

  /**
   * 根据物料 ID 列表获取所有版本的描述
   */
  async getDescriptionsByMaterialIds(
    materialIds: number[],
  ): Promise<Record<number, Service.MaterialSmartDescription.RawDescriptionQueryResult[]>> {
    if (materialIds.length === 0) {
      return {}
    }

    try {
      const sql = `
        SELECT *
        FROM material_smart_description
        WHERE material_id IN (${materialIds.join(',')}) AND state = 1
        ORDER BY material_id, create_time DESC
      `

      const results = await this.databaseService.query(sql)
      const descriptions = results as Service.MaterialSmartDescription.RawDescriptionQueryResult[]

      // 按 materialId 分组
      const grouped: Record<number, Service.MaterialSmartDescription.RawDescriptionQueryResult[]> = {}
      descriptions.forEach((desc) => {
        if (!grouped[desc.material_id]) {
          grouped[desc.material_id] = []
        }
        grouped[desc.material_id].push(desc)
      })

      return grouped
    }
    catch (error) {
      this.logger.error('根据物料ID列表获取描述失败', error)
      throw error
    }
  }
}
