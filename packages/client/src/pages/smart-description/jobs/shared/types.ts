import type { MaterialSmartDescriptionJob, PaginationParams, JobSearchParams } from '../../../../services/api'

// 任务统计数据类型
export interface JobStats {
  pending: number
  succeeded: number
  failed: number
  total: number
}

// 物料信息类型
export interface MaterialInfo {
  title: string    // 中文名称
  name: string     // 英文名称（组件名称）
  namespace: string
  version: string
}

// 触发任务参数类型
export interface TriggerJobParams {
  materialId?: string
  namespace?: string
  materialVersionName: string
  invokeLangBridgeBizKey: string
  invokeLangBridgeOperator: string
}

// 列表相关类型
export interface JobListState {
  data: MaterialSmartDescriptionJob[]
  loading: boolean
  total: number
  currentPage: number
  pageSize: number
  statsData: JobStats
}

export interface JobListActions {
  fetchData: (params?: Partial<PaginationParams>) => Promise<void>
  fetchStats: () => Promise<void>
  handlePageChange: (page: number, size?: number) => void
  handleRefresh: () => void
}

// 详情相关类型
export interface JobDetailState {
  selectedRecord: MaterialSmartDescriptionJob | null
  detailVisible: boolean
}

export interface JobDetailActions {
  showDetail: (record: MaterialSmartDescriptionJob) => void
  closeDetail: () => void
}

// 触发任务相关类型
export interface JobTriggerState {
  triggerVisible: boolean
  triggerLoading: boolean
}

export interface JobTriggerActions {
  showTrigger: () => void
  closeTrigger: () => void
  handleTriggerJob: (values: TriggerJobParams) => Promise<boolean>
}

// 历史记录相关类型
export interface JobHistoryState {
  historyVisible: boolean
  selectedMaterialPubId: number | null
  selectedMaterialInfo: MaterialInfo | null
}

export interface JobHistoryActions {
  showHistory: (record: MaterialSmartDescriptionJob) => void
  closeHistory: () => void
}

// 组件 Props 类型
export interface JobListSectionProps {
  onViewDetail: (record: MaterialSmartDescriptionJob) => void
  onViewHistory: (record: MaterialSmartDescriptionJob) => void
}

export interface JobListSectionRef {
  refresh: () => void
  refreshStats: () => void
}

export interface JobDetailSectionRef {
  showDetail: (record: MaterialSmartDescriptionJob) => void
}

export interface JobTriggerSectionRef {
  showTrigger: () => void
}

export interface JobTriggerSectionProps {
  onSuccess: () => void
}

export interface JobHistorySectionRef {
  showHistory: (record: MaterialSmartDescriptionJob) => void
}

export interface JobHistorySectionProps {
  onViewDetail: (record: MaterialSmartDescriptionJob) => void
}

// 重新导出常用类型
export type { MaterialSmartDescriptionJob, PaginationParams, JobSearchParams }
