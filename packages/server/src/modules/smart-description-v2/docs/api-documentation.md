# Smart Description V2 API Documentation

## 概述

智能描述 V2 API 基于领域驱动设计（DDD）架构重新构建，提供标准化的 RESTful 接口用于管理组件的智能描述信息。

### 核心特性

- **标准化响应格式**：所有 API 响应都遵循统一的 JSON 格式
- **完整的错误处理**：提供详细的错误信息和状态码
- **批量操作支持**：支持批量状态更新和并发处理
- **版本回退机制**：智能查找可用的描述版本
- **权限控制**：基于状态的操作权限管理
- **分页查询**：支持灵活的分页、搜索和过滤

### 基础 URL

```
https://api.example.com/api/v2/smart-descriptions
```

## 标准响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 实际数据内容
  },
  "timestamp": 1703123456789,
  "traceId": "req-123456"
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "Validation failed",
  "data": null,
  "timestamp": 1703123456789,
  "details": {
    "errors": ["Field 'content' is required"]
  }
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "data": [
      // 数据项列表
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## API 端点

### 1. 获取智能描述列表

**GET** `/api/v2/smart-descriptions`

获取智能描述的分页列表，支持多种过滤条件。

#### 查询参数

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| page | number | 否 | 页码，从1开始 | 1 |
| pageSize | number | 否 | 每页大小，最大100 | 20 |
| sortBy | string | 否 | 排序字段 | updateTime |
| sortOrder | string | 否 | 排序方向 | desc |
| keyword | string | 否 | 搜索关键词 | Button |
| materialId | number | 否 | 物料ID | 123 |
| namespace | string | 否 | 命名空间 | antd |
| publishStatus | string | 否 | 发布状态 | published |
| createdBy | string | 否 | 创建者 | user123 |
| startTime | string | 否 | 开始时间 | 2023-01-01T00:00:00Z |
| endTime | string | 否 | 结束时间 | 2023-12-31T23:59:59Z |

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "data": [
      {
        "id": 1,
        "materialInfo": {
          "id": 123,
          "title": "按钮组件",
          "name": "Button",
          "namespace": "antd",
          "version": "1.0.0"
        },
        "content": {
          "exportType": {
            "type": "component",
            "explanation": "可复用的按钮组件"
          }
          // ... 其他内容
        },
        "zhidaDescription": {
          "namespace": "antd",
          "title": "按钮组件",
          "name": "Button",
          "description": "可复用的按钮组件"
        },
        "publishStatus": "published",
        "metadata": {
          "createTime": 1703123456789,
          "updateTime": 1703123456789,
          "version": 1,
          "createdBy": "user123"
        },
        "permissions": {
          "canEdit": false,
          "canPublish": false,
          "canArchive": true
        }
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 1,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 2. 获取物料描述分组列表

**GET** `/api/v2/smart-descriptions/materials`

按物料分组显示智能描述，每个物料显示最新描述和版本统计。

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "data": [
      {
        "materialInfo": {
          "id": 123,
          "title": "按钮组件",
          "name": "Button",
          "namespace": "antd"
        },
        "latestDescription": {
          "id": 1,
          "publishStatus": "published",
          "updateTime": 1703123456789,
          "version": "1.0.0"
        },
        "versionCount": 3,
        "versions": [
          {
            "id": 1,
            "version": "1.0.0",
            "publishStatus": "published",
            "updateTime": 1703123456789,
            "createdBy": "user123"
          }
          // ... 其他版本
        ]
      }
    ]
  }
}
```

### 3. 获取智能描述详情

**GET** `/api/v2/smart-descriptions/{id}`

根据ID获取智能描述的完整信息。

#### 路径参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| id | number | 是 | 描述ID |

### 4. 创建智能描述

**POST** `/api/v2/smart-descriptions`

创建新的智能描述。

#### 请求体示例

```json
{
  "materialId": 123,
  "materialVersion": "1.0.0",
  "content": {
    "exportType": {
      "type": "component",
      "explanation": "可复用的按钮组件"
    },
    "functionality": {
      "features": ["点击交互", "多种样式"]
    },
    "scenarios": {
      "cases": ["表单提交", "页面导航"]
    },
    "uiStructure": {
      "styleFeatures": ["现代化", "简洁"]
    },
    "usage": {
      "basicExample": ["<Button>点击</Button>"]
    },
    "api": {
      "parameters": {
        "typescriptCode": "interface ButtonProps { onClick?: () => void }"
      },
      "returnValue": {
        "type": "JSX.Element",
        "description": "按钮组件"
      }
    },
    "considerations": {
      "limitations": ["不支持IE11"],
      "performance": ["轻量级"]
    },
    "bestPractices": {
      "recommendations": ["使用TypeScript"],
      "antiPatterns": ["避免内联样式"]
    },
    "decisiveFactors": {
      "advantages": ["类型安全"],
      "limitations": ["学习成本"]
    }
  },
  "createdBy": "user123"
}
```

### 5. 更新智能描述

**PUT** `/api/v2/smart-descriptions/{id}`

更新智能描述的内容或ZhiDa描述。

### 6. 发布智能描述

**POST** `/api/v2/smart-descriptions/{id}/publish`

将草稿状态的智能描述发布为正式版本。

#### 请求体示例

```json
{
  "publishedBy": "user123"
}
```

### 7. 归档智能描述

**POST** `/api/v2/smart-descriptions/{id}/archive`

将智能描述标记为归档状态。

### 8. 批量更新状态

**POST** `/api/v2/smart-descriptions/batch/status`

批量更新多个智能描述的发布状态。

#### 请求体示例

```json
{
  "ids": [1, 2, 3],
  "descriptionIds": [1, 2, 3],
  "newStatus": "published",
  "operatedBy": "user123",
  "remark": "批量发布操作"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "succeeded": [
      {
        "id": 1,
        "data": {
          // 更新后的描述信息
        }
      }
    ],
    "failed": [
      {
        "id": 2,
        "error": "Description cannot be published"
      }
    ],
    "summary": {
      "total": 3,
      "succeeded": 1,
      "failed": 2,
      "successRate": 33
    }
  }
}
```

### 9. 查找最新已发布描述

**GET** `/api/v2/smart-descriptions/materials/{materialId}/versions/{version}/published`

查找指定物料和版本的已发布描述，支持版本回退机制。

#### 查询参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| namespace | string | 是 | 命名空间 |

#### 响应示例

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "description": {
      // 描述详情
    },
    "isTargetVersion": false,
    "actualVersion": "0.9.0"
  }
}
```

### 10. 健康检查

**GET** `/api/v2/smart-descriptions/health/check`

检查服务运行状态。

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必需字段 |
| 401 | 未授权 | 提供有效的认证信息 |
| 403 | 权限不足 | 确认用户具有相应操作权限 |
| 404 | 资源不存在 | 确认资源ID是否正确 |
| 409 | 资源冲突 | 检查资源状态是否允许当前操作 |
| 422 | 业务规则验证失败 | 根据错误详情调整请求内容 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 使用示例

### TypeScript 客户端示例

```typescript
interface SmartDescriptionClient {
  async getDescriptions(query: SmartDescriptionQuery): Promise<PaginatedResponse<SmartDescriptionView>>
  async createDescription(dto: CreateSmartDescriptionDto): Promise<OperationResponse<SmartDescriptionView>>
  async updateDescription(id: number, dto: UpdateSmartDescriptionDto): Promise<OperationResponse<SmartDescriptionView>>
  async publishDescription(id: number, publishedBy?: string): Promise<OperationResponse<SmartDescriptionView>>
}

// 使用示例
const client = new SmartDescriptionClient()

// 获取列表
const descriptions = await client.getDescriptions({
  page: 1,
  pageSize: 20,
  publishStatus: 'published'
})

// 创建描述
const result = await client.createDescription({
  materialId: 123,
  materialVersion: '1.0.0',
  content: { /* ... */ }
})
```

## 版本历史

- **v2.0.0** - 基于DDD架构的全新设计
  - 标准化响应格式
  - 完整的错误处理
  - 批量操作支持
  - 版本回退机制
